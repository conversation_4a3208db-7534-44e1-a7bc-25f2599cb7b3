#!/usr/bin/env python3
"""
验证测试ID的关联关系
"""
import sys
import os
import requests

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from database.duckdb_manager import DuckDBManager

def verify_test_id_associations(test_id):
    """验证测试ID的关联关系"""
    print(f"=== 验证测试ID: {test_id} ===")
    
    db_manager = DuckDBManager()
    
    # 1. 检查基本信息
    try:
        sql = """
        SELECT member_id, digital_id, user_name, bd_name
        FROM users 
        WHERE digital_id = ?
        """
        results = db_manager.execute_query(sql, [test_id])
        
        if results:
            member_id = results[0][0]
            digital_id = results[0][1]
            user_name = results[0][2] or '未知'
            bd_name = results[0][3] or '未知'
            
            print(f"   基本信息:")
            print(f"     digital_id: {digital_id}")
            print(f"     member_id: {member_id}")
            print(f"     用户名: {user_name}")
            print(f"     BD团队: {bd_name}")
        else:
            print(f"   ❌ 未找到用户信息")
            return False
            
    except Exception as e:
        print(f"   查询用户信息失败: {e}")
        return False
    
    # 2. 检查关联关系
    try:
        # IP共享关系
        sql = """
        SELECT 
            CASE 
                WHEN user_a_mid = ? THEN user_b_mid
                ELSE user_a_mid 
            END as related_user,
            shared_value as shared_ip,
            CASE 
                WHEN user_a_mid = ? THEN user_b_bd
                ELSE user_a_bd 
            END as bd_name
        FROM shared_relationships 
        WHERE relationship_type = 'ip' 
            AND (user_a_mid = ? OR user_b_mid = ?)
        LIMIT 5
        """
        ip_results = db_manager.execute_query(sql, [test_id, test_id, test_id, test_id])
        
        if ip_results:
            print(f"\n   🌐 IP共享关系 ({len(ip_results)} 个):")
            for row in ip_results:
                related_user = row[0]
                shared_ip = row[1]
                bd_name = row[2] or '未知'
                print(f"     与用户 {related_user} 共享IP: {shared_ip} [BD: {bd_name}]")
        
        # 设备共享关系
        sql = """
        SELECT 
            CASE 
                WHEN user_a_mid = ? THEN user_b_mid
                ELSE user_a_mid 
            END as related_user,
            shared_value as shared_device,
            CASE 
                WHEN user_a_mid = ? THEN user_b_bd
                ELSE user_a_bd 
            END as bd_name
        FROM shared_relationships 
        WHERE relationship_type = 'device' 
            AND (user_a_mid = ? OR user_b_mid = ?)
        LIMIT 5
        """
        device_results = db_manager.execute_query(sql, [test_id, test_id, test_id, test_id])
        
        if device_results:
            print(f"\n   📱 设备共享关系 ({len(device_results)} 个):")
            for row in device_results:
                related_user = row[0]
                shared_device = row[1]
                bd_name = row[2] or '未知'
                device_short = shared_device[:16] + "..." if len(shared_device) > 16 else shared_device
                print(f"     与用户 {related_user} 共享设备: {device_short} [BD: {bd_name}]")
        
        # 统计关系数量
        sql = """
        SELECT relationship_type, COUNT(*) as count
        FROM shared_relationships 
        WHERE (user_a_mid = ? OR user_b_mid = ?)
        GROUP BY relationship_type
        """
        count_results = db_manager.execute_query(sql, [test_id, test_id])
        
        if count_results:
            print(f"\n   📊 关系统计:")
            total_relations = 0
            for row in count_results:
                rel_type = row[0]
                count = row[1]
                total_relations += count
                type_name = {"ip": "IP共享", "device": "设备共享", "both": "同时共享"}
                print(f"     {type_name.get(rel_type, rel_type)}: {count} 个")
            print(f"     总计: {total_relations} 个关系")
            
            return total_relations > 0
        else:
            print(f"   ❌ 没有关联关系")
            return False
            
    except Exception as e:
        print(f"   查询关联关系失败: {e}")
        return False

def test_api_with_digital_id(digital_id):
    """测试API是否能正确处理digital_id"""
    print(f"\n=== 测试API (digital_id: {digital_id}) ===")
    
    try:
        # 登录
        login_url = "http://localhost:5005/api/auth/login"
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post(login_url, json=login_data)
        
        if response.status_code != 200:
            print(f"   ❌ 登录失败: {response.text}")
            return False
        
        cookies = response.cookies
        print("   ✅ 登录成功")
        
        # 获取任务
        tasks_url = "http://localhost:5005/api/user-analysis/available-tasks"
        response = requests.get(tasks_url, cookies=cookies)
        
        if response.status_code != 200:
            print(f"   ❌ 获取任务失败: {response.text}")
            return False
        
        tasks_data = response.json()
        agent_tasks = tasks_data.get('agent_tasks', [])
        
        if not agent_tasks:
            print("   ❌ 没有可用的代理任务")
            return False
        
        agent_task_id = agent_tasks[0]['task_id']
        print(f"   ✅ 使用代理任务: {agent_task_id[:8]}...")
        
        # 测试digital_id搜索
        analysis_url = f"http://localhost:5005/api/user-analysis/search-by-digital/{digital_id}"
        params = {'agent_task_id': agent_task_id, 'include_behavior': 'false'}
        response = requests.get(analysis_url, params=params, cookies=cookies)
        
        if response.status_code == 200:
            analysis_data = response.json()
            
            print("   ✅ digital_id API测试成功:")
            
            # 检查用户信息
            user_profile = analysis_data.get('user_profile', {})
            if user_profile:
                print(f"     用户信息: member_id={user_profile.get('member_id', 'N/A')}")
                print(f"     搜索方法: {user_profile.get('search_method', 'N/A')}")
            
            # 检查关联分析
            associations = analysis_data.get('associations', {})
            if associations:
                print(f"     关联分析:")
                print(f"       同IP用户: {associations.get('same_ip_count', 0)} 个")
                print(f"       同设备用户: {associations.get('same_device_count', 0)} 个")
                print(f"       同时共享用户: {associations.get('both_shared_count', 0)} 个")
                
                # 显示关联用户示例
                same_ip_users = associations.get('same_ip_users', [])
                if same_ip_users:
                    user = same_ip_users[0]
                    print(f"       IP关联示例: {user.get('member_id')} (IP: {user.get('shared_ip', 'N/A')})")
                
                same_device_users = associations.get('same_device_users', [])
                if same_device_users:
                    user = same_device_users[0]
                    device = user.get('shared_device', 'N/A')
                    device_short = device[:16] + "..." if len(device) > 16 else device
                    print(f"       设备关联示例: {user.get('member_id')} (设备: {device_short})")
            
            return True
        else:
            print(f"   ❌ digital_id API测试失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"   API测试异常: {e}")
        return False

def recommend_best_test_id():
    """推荐最佳测试ID"""
    print("\n=== 推荐最佳测试ID ===")
    
    # 测试候选ID
    candidate_ids = ["09333254", "17834484", "00001144", "00001233", "00007366"]
    
    best_candidates = []
    
    for test_id in candidate_ids:
        print(f"\n测试候选ID: {test_id}")
        
        # 验证关联关系
        has_associations = verify_test_id_associations(test_id)
        
        if has_associations:
            # 测试API
            api_works = test_api_with_digital_id(test_id)
            
            if api_works:
                best_candidates.append(test_id)
                print(f"   ✅ {test_id} - 完美的测试候选")
            else:
                print(f"   ⚠️ {test_id} - 有关联关系但API测试失败")
        else:
            print(f"   ❌ {test_id} - 无关联关系")
    
    return best_candidates

if __name__ == "__main__":
    best_ids = recommend_best_test_id()
    
    print(f"\n🎯 最终推荐:")
    if best_ids:
        recommended_id = best_ids[0]
        print(f"   推荐测试ID: {recommended_id}")
        print(f"   该ID既有关联关系，API也工作正常")
        
        print(f"\n📝 测试步骤:")
        print(f"1. 打开个人分析页面:")
        print(f"   http://localhost:3000/src/modules/user-analysis/pages/user-analysis.html")
        print(f"2. 输入ID: {recommended_id}")
        print(f"3. 系统会自动识别为数字ID")
        print(f"4. 选择代理分析任务")
        print(f"5. 点击'智能搜索'")
        print(f"6. 查看关联分析结果")
        print(f"7. 点击不同的标签页查看关联用户")
        print(f"8. 点击关联用户的'查看'按钮测试跳转")
    else:
        print(f"   ❌ 没有找到完美的测试ID")
        print(f"   建议检查数据或系统配置")
