#!/usr/bin/env python3
"""
测试API响应格式
"""
import requests
import json
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

def test_login():
    """测试登录"""
    print("=== 测试登录 ===")
    
    login_url = "http://localhost:5005/api/auth/login"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        print(f"登录状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("登录成功")
            # 获取session cookie
            return response.cookies
        else:
            print(f"登录失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"登录请求失败: {e}")
        return None

def test_user_analysis_api(cookies, user_id="09333254"):
    """测试用户分析API"""
    print(f"\n=== 测试用户分析API (用户ID: {user_id}) ===")
    
    # 首先获取可用任务
    tasks_url = "http://localhost:5005/api/user-analysis/available-tasks"
    
    try:
        response = requests.get(tasks_url, cookies=cookies)
        print(f"获取任务列表状态码: {response.status_code}")
        
        if response.status_code == 200:
            tasks_data = response.json()
            print(f"可用的代理任务数: {len(tasks_data.get('agent_tasks', []))}")
            
            agent_tasks = tasks_data.get('agent_tasks', [])
            if agent_tasks:
                agent_task_id = agent_tasks[0]['task_id']
                print(f"使用代理任务ID: {agent_task_id}")
                
                # 测试用户分析API
                analysis_url = f"http://localhost:5005/api/user-analysis/search/{user_id}"
                params = {
                    'agent_task_id': agent_task_id,
                    'include_behavior': 'false'
                }
                
                print(f"\n请求URL: {analysis_url}")
                print(f"请求参数: {params}")
                
                response = requests.get(analysis_url, params=params, cookies=cookies)
                print(f"用户分析状态码: {response.status_code}")
                
                if response.status_code == 200:
                    analysis_data = response.json()
                    print("\n=== API响应数据结构 ===")
                    
                    # 检查关联分析数据
                    associations = analysis_data.get('associations', {})
                    print(f"关联分析数据存在: {'是' if associations else '否'}")
                    
                    if associations:
                        print(f"  same_ip_count: {associations.get('same_ip_count', '未设置')}")
                        print(f"  same_device_count: {associations.get('same_device_count', '未设置')}")
                        print(f"  both_shared_count: {associations.get('both_shared_count', '未设置')}")
                        
                        same_ip_users = associations.get('same_ip_users', [])
                        same_device_users = associations.get('same_device_users', [])
                        both_shared_users = associations.get('both_shared_users', [])
                        
                        print(f"  same_ip_users数量: {len(same_ip_users)}")
                        print(f"  same_device_users数量: {len(same_device_users)}")
                        print(f"  both_shared_users数量: {len(both_shared_users)}")
                        
                        # 显示示例数据
                        if same_ip_users:
                            print(f"  同IP用户示例: {same_ip_users[0]}")
                        if same_device_users:
                            print(f"  同设备用户示例: {same_device_users[0]}")
                        if both_shared_users:
                            print(f"  同时共享用户示例: {both_shared_users[0]}")
                    
                    # 保存完整响应到文件
                    with open('api_response.json', 'w', encoding='utf-8') as f:
                        json.dump(analysis_data, f, ensure_ascii=False, indent=2)
                    print(f"\n完整API响应已保存到: api_response.json")
                    
                else:
                    print(f"用户分析请求失败: {response.text}")
            else:
                print("没有可用的代理任务")
        else:
            print(f"获取任务列表失败: {response.text}")
            
    except Exception as e:
        print(f"API测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_frontend_format():
    """测试前端期望的数据格式"""
    print("\n=== 前端期望的数据格式 ===")
    
    expected_format = {
        "associations": {
            "same_ip_count": 1,
            "same_device_count": 1, 
            "both_shared_count": 1,
            "same_ip_users": [
                {
                    "member_id": "17834484",
                    "shared_ip": "2a01:5ec0:5816:4676:1:0:d925:e798",
                    "user_name": "",
                    "bd_name": "",
                    "risk_score": 0
                }
            ],
            "same_device_users": [
                {
                    "member_id": "17834484", 
                    "shared_device": "a0d79576063090f21c39bf8d27b0cc4b98fb6ccb795d2be38ea783b6922277d0",
                    "user_name": "",
                    "bd_name": "",
                    "risk_score": 0
                }
            ],
            "both_shared_users": [
                {
                    "member_id": "17834484",
                    "shared_ip": "2a01:5ec0:5816:4676:1:0:d925:e798",
                    "shared_device": "a0d79576063090f21c39bf8d27b0cc4b98fb6ccb795d2be38ea783b6922277d0",
                    "user_name": "",
                    "bd_name": "",
                    "risk_score": 0
                }
            ]
        }
    }
    
    print("前端期望的关联分析数据格式:")
    print(json.dumps(expected_format, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    # 测试登录
    cookies = test_login()
    
    if cookies:
        # 测试API
        test_user_analysis_api(cookies)
    
    # 显示前端期望格式
    test_frontend_format()
    
    print("\n=== 测试完成 ===")
