#!/usr/bin/env python3
"""
查找既有个人交易数据又有共享关系的真实测试用户
"""
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from database.duckdb_manager import DuckDBManager

def find_users_with_both_data():
    """查找既有user_trading_profiles数据又有shared_relationships的用户"""
    print("=== 查找既有个人数据又有共享关系的用户 ===")
    
    db_manager = DuckDBManager()
    
    try:
        # 关键查询：找到既在user_trading_profiles中又在shared_relationships中的用户
        # 注意：user_trading_profiles使用member_id，shared_relationships使用数字ID
        # 需要通过users表进行关联
        sql = """
        SELECT DISTINCT 
            utp.member_id,
            u.digital_id,
            utp.total_volume,
            utp.total_trades,
            utp.win_rate,
            sr_count.relation_count,
            u.bd_name
        FROM user_trading_profiles utp
        INNER JOIN users u ON utp.member_id = u.member_id
        INNER JOIN (
            SELECT 
                CAST(user_a_mid AS VARCHAR) as digital_id, 
                COUNT(*) as relation_count
            FROM shared_relationships 
            WHERE user_a_mid IS NOT NULL
            GROUP BY user_a_mid
            HAVING COUNT(*) >= 1
            
            UNION ALL
            
            SELECT 
                CAST(user_b_mid AS VARCHAR) as digital_id, 
                COUNT(*) as relation_count
            FROM shared_relationships 
            WHERE user_b_mid IS NOT NULL
            GROUP BY user_b_mid
            HAVING COUNT(*) >= 1
        ) sr_count ON u.digital_id = sr_count.digital_id
        WHERE utp.total_volume > 1000 
            AND utp.total_trades > 5
        ORDER BY utp.total_volume DESC, sr_count.relation_count DESC
        LIMIT 10
        """
        
        results = db_manager.execute_query(sql)
        
        if results:
            print("   找到既有个人数据又有共享关系的用户:")
            perfect_users = []
            
            for row in results:
                member_id = row[0]
                digital_id = row[1]
                total_volume = row[2]
                total_trades = row[3]
                win_rate = row[4]
                relation_count = row[5]
                bd_name = row[6] or '未知'
                
                perfect_users.append({
                    'member_id': member_id,
                    'digital_id': digital_id,
                    'total_volume': total_volume,
                    'total_trades': total_trades,
                    'win_rate': win_rate,
                    'relation_count': relation_count,
                    'bd_name': bd_name
                })
                
                print(f"     ✅ digital_id: {digital_id}")
                print(f"        member_id: {member_id}")
                print(f"        交易量: {total_volume:,.0f} USDT")
                print(f"        交易数: {total_trades}")
                print(f"        胜率: {win_rate:.2%}")
                print(f"        关联关系: {relation_count} 个")
                print(f"        BD团队: {bd_name}")
                print()
            
            return perfect_users
        else:
            print("   ❌ 没有找到既有个人数据又有共享关系的用户")
            return []
            
    except Exception as e:
        print(f"   查询失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def verify_user_details(user_info):
    """验证用户的详细信息"""
    print(f"=== 验证用户详细信息 ===")
    
    db_manager = DuckDBManager()
    member_id = user_info['member_id']
    digital_id = user_info['digital_id']
    
    print(f"验证用户: {digital_id} (member_id: {member_id})")
    
    # 1. 验证user_trading_profiles数据
    try:
        sql = """
        SELECT 
            member_id, total_volume, total_trades, win_rate, 
            profit_factor, return_rate, total_pnl, 
            avg_trade_size, trading_frequency, analysis_date
        FROM user_trading_profiles 
        WHERE member_id = ?
        """
        results = db_manager.execute_query(sql, [member_id])
        
        if results:
            row = results[0]
            print(f"   📊 个人交易数据:")
            print(f"     交易量: {row[1]:,.0f} USDT")
            print(f"     交易数: {row[2]}")
            print(f"     胜率: {row[3]:.2%}")
            print(f"     盈利因子: {row[4]:.2f}")
            print(f"     收益率: {row[5]:.2%}")
            print(f"     总盈亏: {row[6]:,.0f} USDT")
            print(f"     平均交易规模: {row[7]:,.0f} USDT")
            print(f"     交易频率: {row[8]:.2f}")
            print(f"     分析日期: {row[9]}")
        else:
            print(f"   ❌ 无个人交易数据")
            return False
            
    except Exception as e:
        print(f"   查询个人数据失败: {e}")
        return False
    
    # 2. 验证shared_relationships数据
    try:
        # IP共享关系
        sql = """
        SELECT 
            CASE 
                WHEN user_a_mid = ? THEN user_b_mid
                ELSE user_a_mid 
            END as related_user,
            shared_value as shared_ip,
            CASE 
                WHEN user_a_mid = ? THEN user_b_bd
                ELSE user_a_bd 
            END as bd_name
        FROM shared_relationships 
        WHERE relationship_type = 'ip' 
            AND (user_a_mid = ? OR user_b_mid = ?)
        LIMIT 3
        """
        ip_results = db_manager.execute_query(sql, [digital_id, digital_id, digital_id, digital_id])
        
        if ip_results:
            print(f"\n   🌐 IP共享关系 ({len(ip_results)} 个):")
            for row in ip_results:
                related_user = row[0]
                shared_ip = row[1]
                bd_name = row[2] or '未知'
                print(f"     与用户 {related_user} 共享IP: {shared_ip} [BD: {bd_name}]")
        
        # 设备共享关系
        sql = """
        SELECT 
            CASE 
                WHEN user_a_mid = ? THEN user_b_mid
                ELSE user_a_mid 
            END as related_user,
            shared_value as shared_device,
            CASE 
                WHEN user_a_mid = ? THEN user_b_bd
                ELSE user_a_bd 
            END as bd_name
        FROM shared_relationships 
        WHERE relationship_type = 'device' 
            AND (user_a_mid = ? OR user_b_mid = ?)
        LIMIT 3
        """
        device_results = db_manager.execute_query(sql, [digital_id, digital_id, digital_id, digital_id])
        
        if device_results:
            print(f"\n   📱 设备共享关系 ({len(device_results)} 个):")
            for row in device_results:
                related_user = row[0]
                shared_device = row[1]
                bd_name = row[2] or '未知'
                device_short = shared_device[:16] + "..." if len(shared_device) > 16 else shared_device
                print(f"     与用户 {related_user} 共享设备: {device_short} [BD: {bd_name}]")
        
        # 统计关系数量
        sql = """
        SELECT relationship_type, COUNT(*) as count
        FROM shared_relationships 
        WHERE (user_a_mid = ? OR user_b_mid = ?)
        GROUP BY relationship_type
        """
        count_results = db_manager.execute_query(sql, [digital_id, digital_id])
        
        if count_results:
            print(f"\n   📈 关系统计:")
            total_relations = 0
            for row in count_results:
                rel_type = row[0]
                count = row[1]
                total_relations += count
                type_name = {"ip": "IP共享", "device": "设备共享", "both": "同时共享"}
                print(f"     {type_name.get(rel_type, rel_type)}: {count} 个")
            print(f"     总计: {total_relations} 个关系")
            
            return total_relations > 0
        else:
            print(f"   ❌ 没有关联关系")
            return False
            
    except Exception as e:
        print(f"   查询关联关系失败: {e}")
        return False

def test_api_with_perfect_user(user_info):
    """测试API是否能正确处理这个完美用户"""
    print(f"\n=== 测试API (完美用户) ===")
    
    import requests
    
    digital_id = user_info['digital_id']
    member_id = user_info['member_id']
    
    try:
        # 登录
        login_url = "http://localhost:5005/api/auth/login"
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post(login_url, json=login_data)
        cookies = response.cookies
        
        # 获取任务
        tasks_url = "http://localhost:5005/api/user-analysis/available-tasks"
        response = requests.get(tasks_url, cookies=cookies)
        tasks_data = response.json()
        agent_task_id = tasks_data['agent_tasks'][0]['task_id']
        
        print(f"   测试用户: {digital_id}")
        print(f"   对应member_id: {member_id}")
        
        # 测试digital_id搜索
        analysis_url = f"http://localhost:5005/api/user-analysis/search-by-digital/{digital_id}"
        params = {'agent_task_id': agent_task_id, 'include_behavior': 'false'}
        response = requests.get(analysis_url, params=params, cookies=cookies)
        
        print(f"   digital_id API状态码: {response.status_code}")
        
        if response.status_code == 200:
            analysis_data = response.json()
            
            print("   ✅ digital_id API测试成功!")
            
            # 检查用户信息
            user_profile = analysis_data.get('user_profile', {})
            if user_profile:
                print(f"     用户信息:")
                print(f"       member_id: {user_profile.get('member_id', 'N/A')}")
                print(f"       digital_id: {user_profile.get('digital_id', 'N/A')}")
                print(f"       bd_name: {user_profile.get('bd_name', 'N/A')}")
            
            # 检查关联分析
            associations = analysis_data.get('associations', {})
            if associations:
                print(f"     关联分析:")
                print(f"       同IP用户: {associations.get('same_ip_count', 0)} 个")
                print(f"       同设备用户: {associations.get('same_device_count', 0)} 个")
                print(f"       同时共享用户: {associations.get('both_shared_count', 0)} 个")
                
                # 显示关联用户示例
                same_ip_users = associations.get('same_ip_users', [])
                if same_ip_users:
                    user = same_ip_users[0]
                    print(f"       IP关联示例: {user.get('member_id')} (IP: {user.get('shared_ip', 'N/A')})")
                
                same_device_users = associations.get('same_device_users', [])
                if same_device_users:
                    user = same_device_users[0]
                    device = user.get('shared_device', 'N/A')
                    device_short = device[:16] + "..." if len(device) > 16 else device
                    print(f"       设备关联示例: {user.get('member_id')} (设备: {device_short})")
            
            return True
        else:
            print(f"   ❌ digital_id API测试失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   API测试异常: {e}")
        return False

if __name__ == "__main__":
    print("🔍 查找完美的测试用户")
    print("=" * 50)
    
    # 查找完美用户
    perfect_users = find_users_with_both_data()
    
    if perfect_users:
        # 验证第一个用户的详细信息
        best_user = perfect_users[0]
        
        print(f"\n🎯 最佳候选用户: {best_user['digital_id']}")
        
        # 验证详细信息
        has_complete_data = verify_user_details(best_user)
        
        if has_complete_data:
            # 测试API
            api_success = test_api_with_perfect_user(best_user)
            
            if api_success:
                print(f"\n🎉 找到完美测试用户!")
                print(f"   digital_id: {best_user['digital_id']}")
                print(f"   member_id: {best_user['member_id']}")
                print(f"   交易量: {best_user['total_volume']:,.0f} USDT")
                print(f"   关联关系: {best_user['relation_count']} 个")
                print(f"   BD团队: {best_user['bd_name']}")
                
                print(f"\n📝 测试步骤:")
                print(f"1. 打开个人分析页面")
                print(f"2. 输入digital_id: {best_user['digital_id']}")
                print(f"3. 或输入member_id: {best_user['member_id']}")
                print(f"4. 选择代理分析任务")
                print(f"5. 点击搜索")
                print(f"6. 查看完整的个人数据和关联分析结果")
            else:
                print(f"\n⚠️ 用户数据完整但API测试失败")
        else:
            print(f"\n❌ 用户数据不完整")
    else:
        print(f"\n❌ 没有找到完美的测试用户")
        print(f"   可能的原因:")
        print(f"   1. user_trading_profiles和shared_relationships使用不同的ID系统")
        print(f"   2. 数据来源不同，没有重叠的用户")
        print(f"   3. 需要检查数据库中的ID映射关系")
