#!/usr/bin/env python3
"""
测试关联分析查询逻辑
"""
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from database.duckdb_manager import DuckDBManager
from modules.user_analysis.services.unified_data_repository import UnifiedUserDataRepository

def test_database_content():
    """测试数据库内容"""
    print("=== 测试数据库内容 ===")
    
    db_manager = DuckDBManager()
    
    # 检查shared_relationships表
    print("\n1. 检查shared_relationships表:")
    try:
        sql = "SELECT COUNT(*) as total FROM shared_relationships"
        result = db_manager.execute_query(sql)
        print(f"   shared_relationships表总记录数: {result[0][0] if result else 0}")
        
        # 查看任务ID
        sql = "SELECT DISTINCT task_id FROM shared_relationships LIMIT 5"
        result = db_manager.execute_query(sql)
        print(f"   可用的任务ID: {[row[0] for row in result] if result else '无'}")
        
        # 查看关系类型
        sql = "SELECT relationship_type, COUNT(*) FROM shared_relationships GROUP BY relationship_type"
        result = db_manager.execute_query(sql)
        print(f"   关系类型统计: {dict(result) if result else '无'}")
        
    except Exception as e:
        print(f"   查询shared_relationships表失败: {e}")
    
    # 检查agent_analysis表
    print("\n2. 检查agent_analysis表:")
    try:
        sql = "SELECT COUNT(*) as total FROM agent_analysis"
        result = db_manager.execute_query(sql)
        print(f"   agent_analysis表总记录数: {result[0][0] if result else 0}")
        
        # 查看任务ID
        sql = "SELECT task_id, total_users, device_shared_count, ip_shared_count FROM agent_analysis LIMIT 3"
        result = db_manager.execute_query(sql)
        if result:
            print("   最近的分析任务:")
            for row in result:
                print(f"     任务ID: {row[0]}, 用户数: {row[1]}, 设备共享: {row[2]}, IP共享: {row[3]}")
        else:
            print("   无分析任务")
            
    except Exception as e:
        print(f"   查询agent_analysis表失败: {e}")

def test_association_query():
    """测试关联分析查询"""
    print("\n=== 测试关联分析查询 ===")
    
    repository = UnifiedUserDataRepository()
    
    # 获取一个测试用户ID
    db_manager = DuckDBManager()
    try:
        # 从shared_relationships表中获取一个用户ID进行测试
        sql = "SELECT DISTINCT user_a_mid FROM shared_relationships WHERE user_a_mid IS NOT NULL LIMIT 1"
        result = db_manager.execute_query(sql)
        
        if not result:
            print("   没有找到可测试的用户ID")
            return
            
        test_user_id = result[0][0]
        print(f"   使用测试用户ID: {test_user_id}")
        
        # 获取最新的代理分析任务ID
        agent_task_id = repository.get_latest_task_id('agent_analysis')
        print(f"   使用代理分析任务ID: {agent_task_id}")
        
        if not agent_task_id:
            print("   没有找到可用的代理分析任务")
            return
        
        # 测试关联分析查询
        print("\n   测试关联分析查询...")
        associations = repository.get_comprehensive_user_associations(
            test_user_id, '', agent_task_id
        )
        
        print(f"   查询结果:")
        print(f"     同IP用户数: {associations.get('same_ip_count', 0)}")
        print(f"     同设备用户数: {associations.get('same_device_count', 0)}")
        print(f"     同时共享用户数: {associations.get('both_shared_count', 0)}")
        
        # 显示一些具体的关联用户
        same_ip_users = associations.get('same_ip_users', [])
        if same_ip_users:
            print(f"     同IP用户示例 (前3个):")
            for i, user in enumerate(same_ip_users[:3]):
                print(f"       {i+1}. {user.get('member_id')} - IP: {user.get('shared_ip')}")
        
        same_device_users = associations.get('same_device_users', [])
        if same_device_users:
            print(f"     同设备用户示例 (前3个):")
            for i, user in enumerate(same_device_users[:3]):
                print(f"       {i+1}. {user.get('member_id')} - 设备: {user.get('shared_device')}")
                
    except Exception as e:
        print(f"   关联分析查询失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_database_content()
    test_association_query()
    print("\n=== 测试完成 ===")
