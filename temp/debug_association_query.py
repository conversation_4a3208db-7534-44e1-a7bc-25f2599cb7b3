#!/usr/bin/env python3
"""
调试关联分析查询问题
"""
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from database.duckdb_manager import <PERSON><PERSON><PERSON>ana<PERSON>

def debug_association_query():
    """调试关联分析查询问题"""
    print("=== 调试关联分析查询问题 ===")
    
    db_manager = DuckDBManager()
    
    # 测试用户
    test_digital_id = "23452801"
    test_member_id = "0c4d4bb797d246529f6d21e8f4605ce8"
    
    print(f"测试用户: digital_id={test_digital_id}, member_id={test_member_id}")
    
    # 1. 检查shared_relationships表中的数据
    print(f"\n1. 检查shared_relationships表中的数据:")
    try:
        sql = """
        SELECT 
            user_a_mid, user_b_mid, relationship_type, shared_value,
            user_a_bd, user_b_bd
        FROM shared_relationships 
        WHERE (user_a_mid = ? OR user_b_mid = ?)
        """
        results = db_manager.execute_query(sql, [test_digital_id, test_digital_id])
        
        if results:
            print(f"   找到 {len(results)} 条关联记录:")
            for row in results:
                user_a = row[0]
                user_b = row[1]
                rel_type = row[2]
                shared_value = row[3]
                bd_a = row[4]
                bd_b = row[5]
                print(f"     {user_a} <-> {user_b} ({rel_type}): {shared_value}")
                print(f"       BD: {bd_a} <-> {bd_b}")
        else:
            print(f"   ❌ 没有找到关联记录")
            
    except Exception as e:
        print(f"   查询失败: {e}")
    
    # 2. 测试我们的查询逻辑
    print(f"\n2. 测试我们的查询逻辑:")
    try:
        from modules.user_analysis.services.unified_data_repository import UnifiedUserDataRepository
        
        repository = UnifiedUserDataRepository()
        agent_task_id = repository.get_latest_task_id('agent_analysis')
        
        print(f"   使用agent_task_id: {agent_task_id}")
        
        # 测试同IP用户查询
        print(f"\n   测试同IP用户查询:")
        same_ip_users = repository._get_same_ip_users(test_member_id, agent_task_id)
        print(f"     结果: {len(same_ip_users)} 个用户")
        for user in same_ip_users:
            print(f"       {user}")
        
        # 测试同设备用户查询
        print(f"\n   测试同设备用户查询:")
        same_device_users = repository._get_same_device_users(test_member_id, agent_task_id)
        print(f"     结果: {len(same_device_users)} 个用户")
        for user in same_device_users:
            print(f"       {user}")
        
        # 问题分析：我们的查询使用member_id，但shared_relationships使用digital_id
        print(f"\n   问题分析:")
        print(f"     我们的查询使用: member_id = {test_member_id}")
        print(f"     但shared_relationships使用: digital_id = {test_digital_id}")
        print(f"     这就是为什么查询不到结果的原因！")
        
    except Exception as e:
        print(f"   测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_corrected_query():
    """测试修正后的查询"""
    print(f"\n3. 测试修正后的查询:")
    
    db_manager = DuckDBManager()
    test_digital_id = "23452801"
    
    # 直接使用digital_id查询
    try:
        # IP共享查询
        sql = """
        SELECT DISTINCT 
            CASE 
                WHEN user_a_mid = ? THEN user_b_mid
                ELSE user_a_mid 
            END as member_id,
            CASE 
                WHEN user_a_mid = ? THEN user_b_member_id
                ELSE user_a_member_id 
            END as member_id_alt,
            CASE 
                WHEN user_a_mid = ? THEN user_b_name
                ELSE user_a_name 
            END as user_name,
            shared_value as shared_ip,
            CASE 
                WHEN user_a_mid = ? THEN user_b_bd
                ELSE user_a_bd 
            END as bd_name
        FROM shared_relationships 
        WHERE relationship_type = 'ip'
            AND (user_a_mid = ? OR user_b_mid = ?)
        LIMIT 10
        """
        
        params = [test_digital_id] * 4 + [test_digital_id, test_digital_id]
        results = db_manager.execute_query(sql, params)
        
        print(f"   IP共享查询结果: {len(results) if results else 0} 个")
        if results:
            for row in results:
                print(f"     用户: {row[0]}, IP: {row[3]}, BD: {row[4]}")
        
        # 设备共享查询
        sql = """
        SELECT DISTINCT 
            CASE 
                WHEN user_a_mid = ? THEN user_b_mid
                ELSE user_a_mid 
            END as member_id,
            shared_value as shared_device,
            CASE 
                WHEN user_a_mid = ? THEN user_b_bd
                ELSE user_a_bd 
            END as bd_name
        FROM shared_relationships 
        WHERE relationship_type = 'device'
            AND (user_a_mid = ? OR user_b_mid = ?)
        LIMIT 10
        """
        
        params = [test_digital_id, test_digital_id, test_digital_id, test_digital_id]
        results = db_manager.execute_query(sql, params)
        
        print(f"   设备共享查询结果: {len(results) if results else 0} 个")
        if results:
            for row in results:
                device_short = row[1][:16] + "..." if len(row[1]) > 16 else row[1]
                print(f"     用户: {row[0]}, 设备: {device_short}, BD: {row[2]}")
                
    except Exception as e:
        print(f"   修正查询失败: {e}")

def identify_the_problem():
    """识别问题所在"""
    print(f"\n=== 问题识别 ===")
    
    print(f"问题根源:")
    print(f"1. 我们的关联查询方法使用 member_id (32位16进制)")
    print(f"2. 但 shared_relationships 表使用 digital_id (8位数字)")
    print(f"3. 这导致查询条件不匹配，无法找到关联关系")
    
    print(f"\n解决方案:")
    print(f"1. 修改关联查询方法，使用 digital_id 而不是 member_id")
    print(f"2. 在查询前将 member_id 转换为 digital_id")
    print(f"3. 或者修改查询逻辑，通过 users 表进行关联")
    
    print(f"\n需要修改的方法:")
    print(f"- _get_same_ip_users")
    print(f"- _get_same_device_users") 
    print(f"- _get_related_accounts_by_digital_id")

if __name__ == "__main__":
    debug_association_query()
    test_corrected_query()
    identify_the_problem()
