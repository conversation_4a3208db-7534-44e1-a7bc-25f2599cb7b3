#!/usr/bin/env python3
"""
调试digital_id查找问题
"""
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from database.duckdb_manager import DuckDBManager

def debug_digital_id_lookup():
    """调试digital_id查找问题"""
    print("=== 调试digital_id查找问题 ===")
    
    db_manager = DuckDBManager()
    test_digital_id = "09333254"
    
    print(f"测试digital_id: {test_digital_id}")
    
    # 1. 检查agent_relationships表是否存在
    print("\n1. 检查agent_relationships表:")
    try:
        sql = "SELECT COUNT(*) FROM agent_relationships"
        results = db_manager.execute_query(sql)
        if results:
            count = results[0][0]
            print(f"   agent_relationships表存在，记录数: {count}")
            
            # 查看表结构
            sql = "DESCRIBE agent_relationships"
            results = db_manager.execute_query(sql)
            if results:
                print("   表结构:")
                for row in results:
                    print(f"     {row[0]}: {row[1]}")
        else:
            print("   agent_relationships表查询失败")
            
    except Exception as e:
        print(f"   agent_relationships表不存在或查询失败: {e}")
    
    # 2. 检查users表中的digital_id查找
    print(f"\n2. 检查users表中的digital_id查找:")
    try:
        sql = """
        SELECT member_id, digital_id, user_name, bd_name
        FROM users 
        WHERE digital_id = ?
        """
        results = db_manager.execute_query(sql, [test_digital_id])
        
        if results:
            print(f"   ✅ 在users表中找到:")
            for row in results:
                print(f"     member_id: {row[0]}")
                print(f"     digital_id: {row[1]}")
                print(f"     user_name: {row[2]}")
                print(f"     bd_name: {row[3]}")
        else:
            print(f"   ❌ 在users表中未找到digital_id: {test_digital_id}")
            
    except Exception as e:
        print(f"   查询users表失败: {e}")
    
    # 3. 测试find_member_id_by_digital_id方法
    print(f"\n3. 测试find_member_id_by_digital_id方法:")
    try:
        from modules.user_analysis.services.unified_data_repository import UnifiedUserDataRepository
        
        repository = UnifiedUserDataRepository()
        
        # 不指定agent_task_id
        member_id = repository.find_member_id_by_digital_id(test_digital_id)
        if member_id:
            print(f"   ✅ 方法返回member_id: {member_id}")
        else:
            print(f"   ❌ 方法返回None")
        
        # 指定agent_task_id
        agent_task_id = repository.get_latest_task_id('agent_analysis')
        if agent_task_id:
            print(f"   使用agent_task_id: {agent_task_id}")
            member_id_with_task = repository.find_member_id_by_digital_id(test_digital_id, agent_task_id)
            if member_id_with_task:
                print(f"   ✅ 带任务ID的方法返回member_id: {member_id_with_task}")
            else:
                print(f"   ❌ 带任务ID的方法返回None")
        else:
            print(f"   ❌ 没有找到agent_task_id")
            
    except Exception as e:
        print(f"   测试方法失败: {e}")
        import traceback
        traceback.print_exc()

def test_direct_api_call():
    """测试直接API调用"""
    print(f"\n=== 测试直接API调用 ===")
    
    import requests
    
    test_digital_id = "09333254"
    
    try:
        # 登录
        login_url = "http://localhost:5005/api/auth/login"
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post(login_url, json=login_data)
        cookies = response.cookies
        
        # 获取任务
        tasks_url = "http://localhost:5005/api/user-analysis/available-tasks"
        response = requests.get(tasks_url, cookies=cookies)
        tasks_data = response.json()
        agent_task_id = tasks_data['agent_tasks'][0]['task_id']
        
        print(f"使用digital_id: {test_digital_id}")
        print(f"使用agent_task_id: {agent_task_id}")
        
        # 测试digital_id API
        analysis_url = f"http://localhost:5005/api/user-analysis/search-by-digital/{test_digital_id}"
        params = {'agent_task_id': agent_task_id}
        
        print(f"请求URL: {analysis_url}")
        print(f"请求参数: {params}")
        
        response = requests.get(analysis_url, params=params, cookies=cookies)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("   ✅ API调用成功")
        else:
            print("   ❌ API调用失败")
            
    except Exception as e:
        print(f"   API测试失败: {e}")

def fix_digital_id_lookup():
    """修复digital_id查找问题"""
    print(f"\n=== 修复建议 ===")
    
    print("问题分析:")
    print("1. find_member_id_by_digital_id方法首先查找agent_relationships表")
    print("2. 但agent_relationships表可能不存在或没有数据")
    print("3. 导致digital_id搜索失败")
    
    print("\n解决方案:")
    print("1. 修改find_member_id_by_digital_id方法，优先使用users表")
    print("2. 或者确保agent_relationships表有正确的数据")
    print("3. 添加更好的错误处理和日志")
    
    print("\n临时解决方案:")
    print("1. 直接使用member_id进行测试")
    print("2. 使用已知的member_id: f9692715fa1748c58e4503b004c1d80a (对应digital_id: 09333254)")

if __name__ == "__main__":
    debug_digital_id_lookup()
    test_direct_api_call()
    fix_digital_id_lookup()
