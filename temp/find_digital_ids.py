#!/usr/bin/env python3
"""
查找可用的digital_id进行测试
"""
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from database.duckdb_manager import Duck<PERSON><PERSON>ana<PERSON>

def find_digital_ids_with_associations():
    """查找有关联关系的digital_id"""
    print("=== 查找有关联关系的digital_id ===")
    
    db_manager = DuckDBManager()
    
    try:
        # 查找shared_relationships表中的用户，看看是否有对应的digital_id
        sql = """
        SELECT DISTINCT 
            sr.user_a_mid as member_id,
            u.digital_id,
            COUNT(sr.id) as relation_count
        FROM shared_relationships sr
        LEFT JOIN users u ON sr.user_a_mid = u.member_id
        WHERE u.digital_id IS NOT NULL 
            AND u.digital_id != ''
            AND sr.user_a_mid IS NOT NULL
        GROUP BY sr.user_a_mid, u.digital_id
        HAVING COUNT(sr.id) > 0
        ORDER BY COUNT(sr.id) DESC
        LIMIT 10
        """
        
        results = db_manager.execute_query(sql)
        
        if results:
            print("   找到有关联关系的digital_id:")
            digital_ids = []
            
            for row in results:
                member_id = row[0]
                digital_id = row[1]
                relation_count = row[2]
                
                digital_ids.append(digital_id)
                print(f"     ✅ digital_id: {digital_id}")
                print(f"        member_id: {member_id}")
                print(f"        关联关系: {relation_count} 个")
                print()
            
            return digital_ids
        else:
            print("   ❌ 没有找到有关联关系的digital_id")
            return []
            
    except Exception as e:
        print(f"   查询失败: {e}")
        return []

def find_all_digital_ids():
    """查找所有可用的digital_id"""
    print("\n=== 查找所有digital_id ===")
    
    db_manager = DuckDBManager()
    
    try:
        # 查找users表中的所有digital_id
        sql = """
        SELECT digital_id, member_id, user_name, bd_name
        FROM users 
        WHERE digital_id IS NOT NULL 
            AND digital_id != ''
            AND LENGTH(digital_id) >= 7
        ORDER BY digital_id
        LIMIT 20
        """
        
        results = db_manager.execute_query(sql)
        
        if results:
            print("   所有可用的digital_id (前20个):")
            for row in results:
                digital_id = row[0]
                member_id = row[1]
                user_name = row[2] or '未知'
                bd_name = row[3] or '未知'
                
                print(f"     digital_id: {digital_id} → member_id: {member_id}")
                print(f"       用户名: {user_name}, BD: {bd_name}")
        else:
            print("   ❌ 没有找到digital_id")
            
    except Exception as e:
        print(f"   查询失败: {e}")

def test_digital_id_conversion():
    """测试digital_id转换功能"""
    print("\n=== 测试digital_id转换功能 ===")
    
    db_manager = DuckDBManager()
    
    # 获取一些测试用的digital_id
    try:
        sql = """
        SELECT digital_id, member_id
        FROM users 
        WHERE digital_id IS NOT NULL 
            AND digital_id != ''
            AND LENGTH(digital_id) >= 7
        LIMIT 5
        """
        
        results = db_manager.execute_query(sql)
        
        if results:
            print("   测试digital_id转换:")
            for row in results:
                digital_id = row[0]
                expected_member_id = row[1]
                
                # 测试转换逻辑
                sql_convert = """
                SELECT DISTINCT member_id FROM users 
                WHERE digital_id = ?
                LIMIT 1
                """
                convert_result = db_manager.execute_query(sql_convert, [digital_id])
                
                if convert_result:
                    actual_member_id = convert_result[0][0]
                    status = "✅" if actual_member_id == expected_member_id else "❌"
                    print(f"     {status} {digital_id} → {actual_member_id}")
                else:
                    print(f"     ❌ {digital_id} → 转换失败")
        else:
            print("   ❌ 没有找到测试数据")
            
    except Exception as e:
        print(f"   测试失败: {e}")

def find_digital_ids_with_shared_relationships():
    """查找在shared_relationships表中有记录的digital_id"""
    print("\n=== 查找shared_relationships中的digital_id ===")
    
    db_manager = DuckDBManager()
    
    try:
        # 查找在shared_relationships表中出现的用户的digital_id
        sql = """
        SELECT DISTINCT 
            u.digital_id,
            u.member_id,
            sr_count.relation_count
        FROM users u
        INNER JOIN (
            SELECT user_a_mid as member_id, COUNT(*) as relation_count
            FROM shared_relationships 
            WHERE user_a_mid IS NOT NULL
            GROUP BY user_a_mid
            HAVING COUNT(*) >= 1
        ) sr_count ON u.member_id = sr_count.member_id
        WHERE u.digital_id IS NOT NULL 
            AND u.digital_id != ''
            AND LENGTH(u.digital_id) >= 7
        ORDER BY sr_count.relation_count DESC
        LIMIT 10
        """
        
        results = db_manager.execute_query(sql)
        
        if results:
            print("   在shared_relationships中有记录的digital_id:")
            test_candidates = []
            
            for row in results:
                digital_id = row[0]
                member_id = row[1]
                relation_count = row[2]
                
                test_candidates.append({
                    'digital_id': digital_id,
                    'member_id': member_id,
                    'relation_count': relation_count
                })
                
                print(f"     ✅ digital_id: {digital_id}")
                print(f"        member_id: {member_id}")
                print(f"        关联关系: {relation_count} 个")
                
                # 查看具体的关联关系
                detail_sql = """
                SELECT relationship_type, COUNT(*) as count
                FROM shared_relationships 
                WHERE (user_a_mid = ? OR user_b_mid = ?)
                GROUP BY relationship_type
                """
                detail_results = db_manager.execute_query(detail_sql, [member_id, member_id])
                
                if detail_results:
                    print(f"        关系详情:")
                    for detail_row in detail_results:
                        rel_type = detail_row[0]
                        count = detail_row[1]
                        type_name = {"ip": "IP共享", "device": "设备共享", "both": "同时共享"}
                        print(f"          {type_name.get(rel_type, rel_type)}: {count} 个")
                print()
            
            return test_candidates
        else:
            print("   ❌ 没有找到在shared_relationships中有记录的digital_id")
            return []
            
    except Exception as e:
        print(f"   查询失败: {e}")
        return []

def recommend_test_digital_ids():
    """推荐测试用的digital_id"""
    print("\n=== 推荐测试digital_id ===")
    
    # 查找最佳测试候选
    candidates = find_digital_ids_with_shared_relationships()
    
    if candidates:
        best_candidate = candidates[0]
        print(f"🎯 推荐测试digital_id: {best_candidate['digital_id']}")
        print(f"   对应member_id: {best_candidate['member_id']}")
        print(f"   关联关系数: {best_candidate['relation_count']}")
        
        print(f"\n📝 测试步骤:")
        print(f"1. 打开个人分析页面: http://localhost:3000/src/modules/user-analysis/pages/user-analysis.html")
        print(f"2. 输入digital_id: {best_candidate['digital_id']}")
        print(f"3. 系统会自动识别为数字ID")
        print(f"4. 选择代理分析任务")
        print(f"5. 点击搜索")
        print(f"6. 查看关联分析结果")
        
        return best_candidate['digital_id']
    else:
        print("❌ 没有找到合适的测试digital_id")
        return None

if __name__ == "__main__":
    find_digital_ids_with_associations()
    find_all_digital_ids()
    test_digital_id_conversion()
    recommended_id = recommend_test_digital_ids()
    
    if not recommended_id:
        print(f"\n🔄 备选方案:")
        print(f"   使用member_id进行测试: 09333254 或 17834484")
        print(f"   这些用户有确认的关联关系")
