#!/usr/bin/env python3
"""
检查ID映射关系
"""
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from database.duckdb_manager import DuckDBManager

def check_id_systems():
    """检查不同表中的ID系统"""
    print("=== 检查ID系统 ===")
    
    db_manager = DuckDBManager()
    
    # 1. 检查shared_relationships表中的ID格式
    print("\n1. shared_relationships表中的ID格式:")
    try:
        sql = """
        SELECT DISTINCT user_a_mid, user_b_mid
        FROM shared_relationships 
        WHERE user_a_mid IS NOT NULL
        LIMIT 10
        """
        results = db_manager.execute_query(sql)
        
        if results:
            for row in results:
                user_a = row[0]
                user_b = row[1]
                print(f"   user_a_mid: {user_a} (长度: {len(str(user_a))}, 类型: {type(user_a)})")
                print(f"   user_b_mid: {user_b} (长度: {len(str(user_b))}, 类型: {type(user_b)})")
                break  # 只看第一个
        else:
            print("   没有数据")
            
    except Exception as e:
        print(f"   查询失败: {e}")
    
    # 2. 检查users表中的ID格式
    print("\n2. users表中的ID格式:")
    try:
        sql = """
        SELECT member_id, digital_id
        FROM users 
        WHERE digital_id IS NOT NULL
        LIMIT 5
        """
        results = db_manager.execute_query(sql)
        
        if results:
            for row in results:
                member_id = row[0]
                digital_id = row[1]
                print(f"   member_id: {member_id} (长度: {len(str(member_id))})")
                print(f"   digital_id: {digital_id} (长度: {len(str(digital_id))})")
                print()
        else:
            print("   没有数据")
            
    except Exception as e:
        print(f"   查询失败: {e}")

def find_direct_digital_id_matches():
    """查找shared_relationships中的ID是否直接就是digital_id"""
    print("\n=== 查找直接匹配的digital_id ===")
    
    db_manager = DuckDBManager()
    
    try:
        # 检查shared_relationships中的user_a_mid是否在users表的digital_id中存在
        sql = """
        SELECT DISTINCT 
            sr.user_a_mid,
            u.digital_id,
            u.member_id
        FROM shared_relationships sr
        INNER JOIN users u ON CAST(sr.user_a_mid AS VARCHAR) = u.digital_id
        LIMIT 10
        """
        results = db_manager.execute_query(sql)
        
        if results:
            print("   找到匹配的digital_id:")
            for row in results:
                user_a_mid = row[0]
                digital_id = row[1]
                member_id = row[2]
                print(f"   ✅ {user_a_mid} = {digital_id} → member_id: {member_id}")
        else:
            print("   ❌ 没有找到直接匹配的digital_id")
            
    except Exception as e:
        print(f"   查询失败: {e}")

def test_specific_ids():
    """测试特定的ID"""
    print("\n=== 测试特定ID ===")
    
    db_manager = DuckDBManager()
    
    # 测试我们已知有关联关系的ID
    test_ids = ["09333254", "17834484"]
    
    for test_id in test_ids:
        print(f"\n测试ID: {test_id}")
        
        # 1. 检查是否在shared_relationships中
        try:
            sql = """
            SELECT COUNT(*) as count
            FROM shared_relationships 
            WHERE user_a_mid = ? OR user_b_mid = ?
            """
            results = db_manager.execute_query(sql, [test_id, test_id])
            if results:
                count = results[0][0]
                print(f"   shared_relationships中的记录: {count} 个")
            
        except Exception as e:
            print(f"   查询shared_relationships失败: {e}")
        
        # 2. 检查是否在users表的digital_id中
        try:
            sql = """
            SELECT member_id, digital_id
            FROM users 
            WHERE digital_id = ?
            """
            results = db_manager.execute_query(sql, [test_id])
            if results:
                member_id = results[0][0]
                digital_id = results[0][1]
                print(f"   users表中找到: digital_id={digital_id} → member_id={member_id}")
            else:
                print(f"   users表中未找到digital_id={test_id}")
                
        except Exception as e:
            print(f"   查询users表失败: {e}")
        
        # 3. 检查是否在users表的member_id中
        try:
            sql = """
            SELECT member_id, digital_id
            FROM users 
            WHERE member_id = ?
            """
            results = db_manager.execute_query(sql, [test_id])
            if results:
                member_id = results[0][0]
                digital_id = results[0][1]
                print(f"   users表中找到: member_id={member_id} → digital_id={digital_id}")
            else:
                print(f"   users表中未找到member_id={test_id}")
                
        except Exception as e:
            print(f"   查询users表失败: {e}")

def find_working_test_cases():
    """找到可以工作的测试用例"""
    print("\n=== 寻找可工作的测试用例 ===")
    
    db_manager = DuckDBManager()
    
    # 策略：从shared_relationships中取一些ID，看看能否在users表中找到对应的记录
    try:
        sql = """
        SELECT DISTINCT user_a_mid
        FROM shared_relationships 
        WHERE user_a_mid IS NOT NULL
        ORDER BY user_a_mid
        LIMIT 20
        """
        results = db_manager.execute_query(sql)
        
        if results:
            print("   测试shared_relationships中的ID是否可以作为digital_id使用:")
            working_ids = []
            
            for row in results:
                test_id = str(row[0])
                
                # 检查这个ID是否在users表中存在
                user_sql = """
                SELECT member_id, digital_id
                FROM users 
                WHERE digital_id = ? OR member_id = ?
                """
                user_results = db_manager.execute_query(user_sql, [test_id, test_id])
                
                if user_results:
                    member_id = user_results[0][0]
                    digital_id = user_results[0][1]
                    working_ids.append({
                        'test_id': test_id,
                        'member_id': member_id,
                        'digital_id': digital_id
                    })
                    print(f"   ✅ {test_id} → member_id: {member_id}, digital_id: {digital_id}")
                    
                    if len(working_ids) >= 3:  # 找到3个就够了
                        break
            
            if working_ids:
                print(f"\n🎯 推荐测试用例:")
                for case in working_ids:
                    print(f"   ID: {case['test_id']} (可以作为digital_id或member_id使用)")
                    
                return working_ids
            else:
                print("   ❌ 没有找到可工作的测试用例")
                return []
        else:
            print("   没有数据")
            return []
            
    except Exception as e:
        print(f"   查询失败: {e}")
        return []

if __name__ == "__main__":
    check_id_systems()
    find_direct_digital_id_matches()
    test_specific_ids()
    working_cases = find_working_test_cases()
    
    print(f"\n📝 总结:")
    if working_cases:
        best_case = working_cases[0]
        print(f"🎯 推荐测试ID: {best_case['test_id']}")
        print(f"   该ID既有关联关系，又可以在个人分析中使用")
    else:
        print(f"❌ 数据库中的ID系统不匹配")
        print(f"   shared_relationships表使用的ID与users表不对应")
        print(f"   建议使用已知的测试ID: 09333254 或 17834484")
        print(f"   这些ID有关联关系，可以测试关联分析功能")
