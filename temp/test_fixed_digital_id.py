#!/usr/bin/env python3
"""
测试修复后的digital_id查找功能
"""
import sys
import os
import requests

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

def test_fixed_digital_id_lookup():
    """测试修复后的digital_id查找"""
    print("=== 测试修复后的digital_id查找 ===")
    
    try:
        from modules.user_analysis.services.unified_data_repository import UnifiedUserDataRepository
        
        repository = UnifiedUserDataRepository()
        test_digital_id = "09333254"
        
        print(f"测试digital_id: {test_digital_id}")
        
        # 测试不带agent_task_id
        member_id = repository.find_member_id_by_digital_id(test_digital_id)
        if member_id:
            print(f"   ✅ 不带任务ID: {test_digital_id} → {member_id}")
        else:
            print(f"   ❌ 不带任务ID: 查找失败")
        
        # 测试带agent_task_id
        agent_task_id = repository.get_latest_task_id('agent_analysis')
        if agent_task_id:
            member_id_with_task = repository.find_member_id_by_digital_id(test_digital_id, agent_task_id)
            if member_id_with_task:
                print(f"   ✅ 带任务ID: {test_digital_id} → {member_id_with_task}")
            else:
                print(f"   ❌ 带任务ID: 查找失败")
        
        return member_id is not None
        
    except Exception as e:
        print(f"   测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_digital_id_api():
    """测试digital_id API"""
    print(f"\n=== 测试digital_id API ===")
    
    test_digital_id = "09333254"
    
    try:
        # 登录
        login_url = "http://localhost:5005/api/auth/login"
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post(login_url, json=login_data)
        
        if response.status_code != 200:
            print(f"   ❌ 登录失败: {response.text}")
            return False
        
        cookies = response.cookies
        print("   ✅ 登录成功")
        
        # 获取任务
        tasks_url = "http://localhost:5005/api/user-analysis/available-tasks"
        response = requests.get(tasks_url, cookies=cookies)
        
        if response.status_code != 200:
            print(f"   ❌ 获取任务失败: {response.text}")
            return False
        
        tasks_data = response.json()
        agent_tasks = tasks_data.get('agent_tasks', [])
        
        if not agent_tasks:
            print("   ❌ 没有可用的代理任务")
            return False
        
        agent_task_id = agent_tasks[0]['task_id']
        print(f"   ✅ 使用代理任务: {agent_task_id[:8]}...")
        
        # 测试digital_id搜索
        analysis_url = f"http://localhost:5005/api/user-analysis/search-by-digital/{test_digital_id}"
        params = {'agent_task_id': agent_task_id, 'include_behavior': 'false'}
        
        print(f"   请求URL: {analysis_url}")
        print(f"   请求参数: {params}")
        
        response = requests.get(analysis_url, params=params, cookies=cookies)
        
        print(f"   响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            analysis_data = response.json()
            
            print("   ✅ digital_id API测试成功!")
            
            # 检查用户信息
            user_profile = analysis_data.get('user_profile', {})
            if user_profile:
                print(f"     用户信息:")
                print(f"       member_id: {user_profile.get('member_id', 'N/A')}")
                print(f"       digital_id: {user_profile.get('digital_id', 'N/A')}")
                print(f"       bd_name: {user_profile.get('bd_name', 'N/A')}")
                print(f"       搜索方法: {user_profile.get('search_method', 'N/A')}")
            
            # 检查关联分析
            associations = analysis_data.get('associations', {})
            if associations:
                print(f"     关联分析:")
                print(f"       同IP用户: {associations.get('same_ip_count', 0)} 个")
                print(f"       同设备用户: {associations.get('same_device_count', 0)} 个")
                print(f"       同时共享用户: {associations.get('both_shared_count', 0)} 个")
                
                # 显示关联用户示例
                same_ip_users = associations.get('same_ip_users', [])
                if same_ip_users:
                    user = same_ip_users[0]
                    print(f"       IP关联示例: {user.get('member_id')} (IP: {user.get('shared_ip', 'N/A')})")
                
                same_device_users = associations.get('same_device_users', [])
                if same_device_users:
                    user = same_device_users[0]
                    device = user.get('shared_device', 'N/A')
                    device_short = device[:16] + "..." if len(device) > 16 else device
                    print(f"       设备关联示例: {user.get('member_id')} (设备: {device_short})")
                
                both_shared_users = associations.get('both_shared_users', [])
                if both_shared_users:
                    user = both_shared_users[0]
                    print(f"       同时共享示例: {user.get('member_id')}")
            
            return True
        else:
            print(f"   ❌ digital_id API测试失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   API测试异常: {e}")
        return False

def test_member_id_api():
    """测试member_id API作为对比"""
    print(f"\n=== 测试member_id API (对比) ===")
    
    test_member_id = "f9692715fa1748c58e4503b004c1d80a"  # 对应digital_id: 09333254
    
    try:
        # 登录
        login_url = "http://localhost:5005/api/auth/login"
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post(login_url, json=login_data)
        cookies = response.cookies
        
        # 获取任务
        tasks_url = "http://localhost:5005/api/user-analysis/available-tasks"
        response = requests.get(tasks_url, cookies=cookies)
        tasks_data = response.json()
        agent_task_id = tasks_data['agent_tasks'][0]['task_id']
        
        # 测试member_id搜索
        analysis_url = f"http://localhost:5005/api/user-analysis/search/{test_member_id}"
        params = {'agent_task_id': agent_task_id, 'include_behavior': 'false'}
        
        response = requests.get(analysis_url, params=params, cookies=cookies)
        
        print(f"   member_id API状态码: {response.status_code}")
        
        if response.status_code == 200:
            analysis_data = response.json()
            associations = analysis_data.get('associations', {})
            
            print("   ✅ member_id API测试成功!")
            print(f"     同IP用户: {associations.get('same_ip_count', 0)} 个")
            print(f"     同设备用户: {associations.get('same_device_count', 0)} 个")
            print(f"     同时共享用户: {associations.get('both_shared_count', 0)} 个")
            
            return True
        else:
            print(f"   ❌ member_id API测试失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   member_id API测试异常: {e}")
        return False

if __name__ == "__main__":
    print("🔧 测试修复后的digital_id功能")
    print("=" * 50)
    
    # 测试修复后的查找方法
    lookup_success = test_fixed_digital_id_lookup()
    
    if lookup_success:
        # 测试API
        api_success = test_digital_id_api()
        
        # 对比测试
        member_api_success = test_member_id_api()
        
        print(f"\n📊 测试结果总结:")
        print(f"   digital_id查找方法: {'✅ 成功' if lookup_success else '❌ 失败'}")
        print(f"   digital_id API: {'✅ 成功' if api_success else '❌ 失败'}")
        print(f"   member_id API: {'✅ 成功' if member_api_success else '❌ 失败'}")
        
        if api_success:
            print(f"\n🎉 修复成功！现在可以使用digital_id进行测试了！")
            print(f"\n📝 推荐测试步骤:")
            print(f"1. 打开个人分析页面: http://localhost:3000/src/modules/user-analysis/pages/user-analysis.html")
            print(f"2. 输入digital_id: 09333254")
            print(f"3. 系统会自动识别为数字ID")
            print(f"4. 选择代理分析任务")
            print(f"5. 点击'智能搜索'")
            print(f"6. 查看关联分析结果")
        else:
            print(f"\n❌ 还有问题需要进一步调试")
    else:
        print(f"\n❌ digital_id查找方法仍有问题")
