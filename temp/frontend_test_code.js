
// 前端测试代码 - 可以在浏览器控制台中运行

// 1. 测试API调用
async function testAssociationAPI() {
    const testDigitalId = '23452801';
    const agentTaskId = '48fa563f-94cc-4b1b-b746-f7031ace7098'; // 替换为实际任务ID
    
    try {
        const response = await fetch(`/api/user-analysis/search-by-digital/${testDigitalId}?agent_task_id=${agentTaskId}&include_behavior=false`);
        const data = await response.json();
        
        console.log('API响应数据:', data);
        
        // 检查关联分析数据
        const associations = data.associations;
        if (associations) {
            console.log('关联分析数据:');
            console.log('- 同IP用户:', associations.same_ip_count);
            console.log('- 同设备用户:', associations.same_device_count);
            console.log('- 同时共享用户:', associations.both_shared_count);
            
            // 显示关联用户详情
            if (associations.same_ip_users.length > 0) {
                console.log('IP关联用户:', associations.same_ip_users);
            }
            if (associations.same_device_users.length > 0) {
                console.log('设备关联用户:', associations.same_device_users);
            }
            if (associations.both_shared_users.length > 0) {
                console.log('同时共享用户:', associations.both_shared_users);
            }
        }
        
        return data;
    } catch (error) {
        console.error('API调用失败:', error);
        return null;
    }
}

// 2. 测试前端显示函数
function testDisplayAssociations(data) {
    if (!data || !data.associations) {
        console.error('没有关联分析数据');
        return;
    }
    
    const associations = data.associations;
    
    // 模拟前端显示逻辑
    console.log('=== 前端显示测试 ===');
    
    // 显示统计信息
    console.log(`同IP用户数: ${associations.same_ip_count}`);
    console.log(`同设备用户数: ${associations.same_device_count}`);
    console.log(`同时共享用户数: ${associations.both_shared_count}`);
    
    // 显示用户列表
    ['same_ip_users', 'same_device_users', 'both_shared_users'].forEach(listName => {
        const users = associations[listName] || [];
        console.log(`\n${listName}:`);
        users.forEach((user, index) => {
            console.log(`  ${index + 1}. ${user.member_id}`);
            if (user.shared_ip) console.log(`     IP: ${user.shared_ip}`);
            if (user.shared_device) console.log(`     设备: ${user.shared_device.substring(0, 16)}...`);
            if (user.bd_name) console.log(`     BD: ${user.bd_name}`);
        });
    });
}

// 3. 运行测试
console.log('开始前端测试...');
testAssociationAPI().then(data => {
    if (data) {
        testDisplayAssociations(data);
        console.log('✅ 前端测试完成');
    } else {
        console.log('❌ 前端测试失败');
    }
});
