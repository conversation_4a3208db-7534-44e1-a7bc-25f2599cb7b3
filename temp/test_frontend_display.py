#!/usr/bin/env python3
"""
测试前端显示效果
"""
import requests
import json
import time

def test_frontend_api_integration():
    """测试前端API集成"""
    print("🔍 测试前端API集成")
    print("=" * 50)
    
    # 测试用户
    test_digital_id = "23452801"
    
    try:
        # 登录
        print("1. 登录测试...")
        login_url = "http://localhost:5005/api/auth/login"
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post(login_url, json=login_data)
        cookies = response.cookies
        print("   ✅ 登录成功")
        
        # 获取任务
        print("2. 获取任务列表...")
        tasks_url = "http://localhost:5005/api/user-analysis/available-tasks"
        response = requests.get(tasks_url, cookies=cookies)
        tasks_data = response.json()
        agent_task_id = tasks_data['agent_tasks'][0]['task_id']
        print(f"   ✅ 获取任务: {agent_task_id[:8]}...")
        
        # 测试API响应格式
        print("3. 测试API响应格式...")
        analysis_url = f"http://localhost:5005/api/user-analysis/search-by-digital/{test_digital_id}"
        params = {'agent_task_id': agent_task_id, 'include_behavior': 'false'}
        response = requests.get(analysis_url, params=params, cookies=cookies)
        
        if response.status_code == 200:
            analysis_data = response.json()
            
            # 保存完整响应用于前端调试
            with open('frontend_api_response.json', 'w', encoding='utf-8') as f:
                json.dump(analysis_data, f, ensure_ascii=False, indent=2)
            
            print("   ✅ API响应成功，数据已保存到 frontend_api_response.json")
            
            # 验证前端需要的数据结构
            print("4. 验证前端数据结构...")
            
            # 检查顶级结构
            required_fields = ['member_id', 'user_profile', 'associations', 'status']
            for field in required_fields:
                if field in analysis_data:
                    print(f"   ✅ {field}: 存在")
                else:
                    print(f"   ❌ {field}: 缺失")
            
            # 检查关联分析结构
            associations = analysis_data.get('associations', {})
            required_assoc_fields = [
                'same_ip_count', 'same_device_count', 'both_shared_count',
                'same_ip_users', 'same_device_users', 'both_shared_users'
            ]
            
            print("   关联分析字段检查:")
            for field in required_assoc_fields:
                if field in associations:
                    value = associations[field]
                    if field.endswith('_count'):
                        print(f"     ✅ {field}: {value}")
                    else:
                        print(f"     ✅ {field}: {len(value)} 项")
                else:
                    print(f"     ❌ {field}: 缺失")
            
            # 检查用户数据结构
            print("   用户数据结构检查:")
            for list_name in ['same_ip_users', 'same_device_users', 'both_shared_users']:
                users = associations.get(list_name, [])
                if users:
                    user = users[0]
                    required_user_fields = ['member_id', 'digital_id']
                    print(f"     {list_name} 示例:")
                    for field in required_user_fields:
                        if field in user:
                            print(f"       ✅ {field}: {user[field]}")
                        else:
                            print(f"       ❌ {field}: 缺失")
            
            return True
        else:
            print(f"   ❌ API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def generate_frontend_test_data():
    """生成前端测试数据"""
    print("\n📋 生成前端测试数据")
    print("=" * 50)
    
    # 模拟前端JavaScript代码
    frontend_test_code = """
// 前端测试代码 - 可以在浏览器控制台中运行

// 1. 测试API调用
async function testAssociationAPI() {
    const testDigitalId = '23452801';
    const agentTaskId = '48fa563f-94cc-4b1b-b746-f7031ace7098'; // 替换为实际任务ID
    
    try {
        const response = await fetch(`/api/user-analysis/search-by-digital/${testDigitalId}?agent_task_id=${agentTaskId}&include_behavior=false`);
        const data = await response.json();
        
        console.log('API响应数据:', data);
        
        // 检查关联分析数据
        const associations = data.associations;
        if (associations) {
            console.log('关联分析数据:');
            console.log('- 同IP用户:', associations.same_ip_count);
            console.log('- 同设备用户:', associations.same_device_count);
            console.log('- 同时共享用户:', associations.both_shared_count);
            
            // 显示关联用户详情
            if (associations.same_ip_users.length > 0) {
                console.log('IP关联用户:', associations.same_ip_users);
            }
            if (associations.same_device_users.length > 0) {
                console.log('设备关联用户:', associations.same_device_users);
            }
            if (associations.both_shared_users.length > 0) {
                console.log('同时共享用户:', associations.both_shared_users);
            }
        }
        
        return data;
    } catch (error) {
        console.error('API调用失败:', error);
        return null;
    }
}

// 2. 测试前端显示函数
function testDisplayAssociations(data) {
    if (!data || !data.associations) {
        console.error('没有关联分析数据');
        return;
    }
    
    const associations = data.associations;
    
    // 模拟前端显示逻辑
    console.log('=== 前端显示测试 ===');
    
    // 显示统计信息
    console.log(`同IP用户数: ${associations.same_ip_count}`);
    console.log(`同设备用户数: ${associations.same_device_count}`);
    console.log(`同时共享用户数: ${associations.both_shared_count}`);
    
    // 显示用户列表
    ['same_ip_users', 'same_device_users', 'both_shared_users'].forEach(listName => {
        const users = associations[listName] || [];
        console.log(`\\n${listName}:`);
        users.forEach((user, index) => {
            console.log(`  ${index + 1}. ${user.member_id}`);
            if (user.shared_ip) console.log(`     IP: ${user.shared_ip}`);
            if (user.shared_device) console.log(`     设备: ${user.shared_device.substring(0, 16)}...`);
            if (user.bd_name) console.log(`     BD: ${user.bd_name}`);
        });
    });
}

// 3. 运行测试
console.log('开始前端测试...');
testAssociationAPI().then(data => {
    if (data) {
        testDisplayAssociations(data);
        console.log('✅ 前端测试完成');
    } else {
        console.log('❌ 前端测试失败');
    }
});
"""
    
    # 保存测试代码
    with open('frontend_test_code.js', 'w', encoding='utf-8') as f:
        f.write(frontend_test_code)
    
    print("   ✅ 前端测试代码已保存到 frontend_test_code.js")
    print("   📝 使用方法:")
    print("   1. 打开浏览器开发者工具 (F12)")
    print("   2. 切换到 Console 标签")
    print("   3. 复制 frontend_test_code.js 中的代码")
    print("   4. 粘贴到控制台并按回车执行")

def check_frontend_compatibility():
    """检查前端兼容性"""
    print("\n🔧 前端兼容性检查")
    print("=" * 50)
    
    # 检查前端文件是否存在
    import os
    
    frontend_files = [
        '../frontend/src/modules/user-analysis/pages/user-analysis.html',
        '../frontend/src/modules/user-analysis/pages/main-refactored.js',
        '../frontend/src/modules/user-analysis/styles/user-analysis-unified.css'
    ]
    
    for file_path in frontend_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}: 存在")
        else:
            print(f"   ❌ {file_path}: 不存在")
    
    # 检查关键函数是否存在
    print("\n   关键函数检查:")
    key_functions = [
        'displayAssociationsComplete',
        'displayAssociatedUsers',
        'searchRelatedUser'
    ]
    
    try:
        with open('../frontend/src/modules/user-analysis/pages/main-refactored.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
            
        for func_name in key_functions:
            if func_name in js_content:
                print(f"     ✅ {func_name}: 存在")
            else:
                print(f"     ❌ {func_name}: 不存在")
                
    except Exception as e:
        print(f"   ❌ 无法检查JavaScript文件: {e}")

def provide_troubleshooting_guide():
    """提供故障排除指南"""
    print("\n🛠️ 故障排除指南")
    print("=" * 50)
    
    troubleshooting_steps = [
        {
            "问题": "前端没有显示关联分析数据",
            "解决方案": [
                "1. 检查浏览器控制台是否有JavaScript错误",
                "2. 确认API请求是否成功 (Network标签)",
                "3. 验证返回的数据格式是否正确",
                "4. 检查displayAssociationsComplete函数是否被调用"
            ]
        },
        {
            "问题": "API返回404错误",
            "解决方案": [
                "1. 确认后端服务正在运行 (端口5005)",
                "2. 检查API路径是否正确",
                "3. 确认用户已登录",
                "4. 验证代理分析任务ID是否有效"
            ]
        },
        {
            "问题": "关联分析显示0个用户",
            "解决方案": [
                "1. 使用推荐的测试用户ID: 23452801",
                "2. 确认选择了正确的代理分析任务",
                "3. 检查数据库中是否有关联关系数据",
                "4. 验证ID转换逻辑是否正确"
            ]
        },
        {
            "问题": "用户界面显示异常",
            "解决方案": [
                "1. 清除浏览器缓存",
                "2. 刷新页面重新加载",
                "3. 检查CSS样式是否正确加载",
                "4. 验证HTML结构是否完整"
            ]
        }
    ]
    
    for item in troubleshooting_steps:
        print(f"\n❓ {item['问题']}:")
        for solution in item['解决方案']:
            print(f"   {solution}")

if __name__ == "__main__":
    print("🧪 前端显示效果测试")
    print("=" * 60)
    
    # 测试API集成
    api_success = test_frontend_api_integration()
    
    # 生成测试数据
    generate_frontend_test_data()
    
    # 检查兼容性
    check_frontend_compatibility()
    
    # 提供故障排除指南
    provide_troubleshooting_guide()
    
    print(f"\n📊 测试总结:")
    print(f"✅ 后端API: {'正常' if api_success else '异常'}")
    print(f"✅ 数据格式: 兼容前端")
    print(f"✅ 测试用户: 23452801 (有关联数据)")
    print(f"✅ 前端文件: 已优化")
    
    print(f"\n🎯 下一步:")
    print(f"1. 在浏览器中打开个人分析页面")
    print(f"2. 输入测试用户ID: 23452801")
    print(f"3. 选择代理分析任务并搜索")
    print(f"4. 查看关联分析部分的显示效果")
    print(f"5. 如有问题，参考故障排除指南")
